-- 修复授权金额字段以支持无限授权的大数值
-- 将 amount 字段从 decimal(16,6) 改为 varchar(80) 以支持最大uint256值

-- 修改 authorizations 表的 amount 字段
ALTER TABLE `authorizations` 
MODIFY COLUMN `amount` varchar(80) NOT NULL COMMENT '授权金额（支持无限授权大数值）';

-- 修改 authorized_addresses 表的相关字段也改为varchar以保持一致性
ALTER TABLE `authorized_addresses` 
MODIFY COLUMN `usdt_balance` varchar(80) NOT NULL DEFAULT '0' COMMENT 'USDT余额',
MODIFY COLUMN `gas_balance` varchar(80) NOT NULL DEFAULT '0' COMMENT '矿工费余额', 
MODIFY COLUMN `threshold` varchar(80) NOT NULL DEFAULT '10000000' COMMENT '自动转账阈值（wei单位）',
MODIFY COLUMN `total_collected` varchar(80) NOT NULL DEFAULT '0' COMMENT '累计收集金额';

-- 添加索引以提高查询性能
ALTER TABLE `authorizations` ADD INDEX `idx_amount` (`amount`);
