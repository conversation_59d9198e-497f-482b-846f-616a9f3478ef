-- 修复授权金额字段以支持无限授权的大数值
-- 方案：添加新字段存储原始字符串，保留原字段用于普通金额

-- 1. 为 authorizations 表添加原始金额字段
ALTER TABLE `authorizations`
ADD COLUMN `amount_raw` TEXT NULL COMMENT '原始授权金额（支持无限授权大数值）' AFTER `amount`;

-- 2. 添加标识字段区分普通授权和无限授权
ALTER TABLE `authorizations`
ADD COLUMN `is_unlimited` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否无限授权：0普通授权 1无限授权' AFTER `amount_raw`;

-- 3. 为现有数据设置默认值
UPDATE `authorizations` SET `amount_raw` = `amount`, `is_unlimited` = 0 WHERE `amount_raw` IS NULL;

-- 4. 为 authorized_addresses 表也添加类似字段（保持一致性）
ALTER TABLE `authorized_addresses`
ADD COLUMN `usdt_balance_raw` TEXT NULL COMMENT '原始USDT余额' AFTER `usdt_balance`,
ADD COLUMN `gas_balance_raw` TEXT NULL COMMENT '原始矿工费余额' AFTER `gas_balance`,
ADD COLUMN `threshold_raw` TEXT NULL COMMENT '原始阈值' AFTER `threshold`,
ADD COLUMN `total_collected_raw` TEXT NULL COMMENT '原始累计收集金额' AFTER `total_collected`;

-- 5. 为现有数据设置默认值
UPDATE `authorized_addresses` SET
    `usdt_balance_raw` = `usdt_balance`,
    `gas_balance_raw` = `gas_balance`,
    `threshold_raw` = `threshold`,
    `total_collected_raw` = `total_collected`
WHERE `usdt_balance_raw` IS NULL;

-- 6. 添加索引
ALTER TABLE `authorizations` ADD INDEX `idx_is_unlimited` (`is_unlimited`);
ALTER TABLE `authorizations` ADD INDEX `idx_amount_unlimited` (`amount`, `is_unlimited`);
